import apiService from './api.js'
import { clearUserPendingPaymentConfirmations } from '../utils/localStorage.js'

/**
 * Authentication service for handling user authentication
 */
class AuthService {
  /**
   * Register a new user
   */
  async register(userData) {
    try {
      const response = await apiService.post('/auth/register', userData)

      // Extract token from nested data object
      if (response.success && response.data && response.data.token) {
        apiService.setAuthToken(response.data.token, userData.rememberMe)
      }

      return response
    } catch (error) {
      console.error('Registration error:', error)
      throw error
    }
  }

  /**
   * Login user
   */
  async login(credentials) {
    try {
      const response = await apiService.post('/auth/login', credentials)

      // Extract token from nested data object
      if (response.success && response.data && response.data.token) {
        console.log('Login successful, storing token:', response.data.token.substring(0, 20) + '...')
        apiService.setAuthToken(response.data.token, credentials.rememberMe)
      } else {
        console.warn('Login response missing token:', response)
      }

      return response
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  /**
   * Logout user
   */
  async logout() {
    try {
      // Get current user ID before clearing auth
      const currentUser = await this.getCurrentUser()
      const userId = currentUser?._id || currentUser?.id

      // Clear user-specific payment confirmations before logout
      if (userId) {
        clearUserPendingPaymentConfirmations(userId)
      }

      await apiService.post('/auth/logout')
    } catch (error) {
      console.error('Logout error:', error)
      // Continue with local logout even if API call fails
    } finally {
      apiService.removeAuthToken()
      // Clear any other user data from storage
      localStorage.removeItem('rememberedUser')
      sessionStorage.removeItem('currentUser')
      localStorage.removeItem('currentUser') // backward compatibility
    }
  }

  /**
   * Verify current token
   */
  async verifyToken() {
    try {
      const response = await apiService.get('/auth/verify')
      return response
    } catch (error) {
      console.error('Token verification error:', error)
      apiService.removeAuthToken()
      throw error
    }
  }

  /**
   * Request password reset
   */
  async forgotPassword(email) {
    try {
      const response = await apiService.post('/auth/forgot-password', { email })
      return response
    } catch (error) {
      console.error('Forgot password error:', error)
      throw error
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token, newPassword) {
    try {
      const response = await apiService.post('/auth/reset-password', {
        token,
        password: newPassword
      })
      return response
    } catch (error) {
      console.error('Reset password error:', error)
      throw error
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return !!apiService.getAuthToken()
  }

  /**
   * Get current user info from token
   */
  async getCurrentUser() {
    if (!this.isAuthenticated()) {
      return null
    }

    try {
      const response = await this.verifyToken()
      // Handle nested response structure
      return response.data ? response.data.user : response.user
    } catch (error) {
      return null
    }
  }

  /**
   * Change password
   */
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await apiService.put('/users/password', {
        currentPassword,
        newPassword
      })
      return response
    } catch (error) {
      console.error('Change password error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const authService = new AuthService()
export default authService
