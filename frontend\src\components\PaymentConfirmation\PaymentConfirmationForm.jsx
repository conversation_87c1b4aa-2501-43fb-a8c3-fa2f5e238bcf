import { useState, useRef, useEffect } from 'react';
import { FiUpload, FiX, FiFile } from 'react-icons/fi';
import { useToast } from '../../contexts/ToastContext';
import { savePendingPaymentConfirmation, loadPendingPaymentConfirmation, removePendingPaymentConfirmation } from '../../utils/localStorage';
import { authService } from '../../services';
import requestLock, { OPERATIONS } from '../../utils/requestLock';

const PaymentConfirmationForm = ({
  onSubmit,
  onCancel = null,
  branding,
  orderData = null,
  appointmentData = null,
  initialData = null,
  isEditing = false
}) => {
  const { showSuccess, showError } = useToast();
  const fileInputRef = useRef(null);

  // Extract appointment details from the API response structure
  const appointment = appointmentData?.appointment || appointmentData;
  const appointmentId = appointment?._id;
  const service = appointment?.service;
  const servicePrice = service?.price || 0;

  // Get current user information using authService
  const [currentUser, setCurrentUser] = useState(null);
  const [currentUserId, setCurrentUserId] = useState(null);

  // Load current user on component mount and listen for login events
  useEffect(() => {
    const loadCurrentUser = async () => {
      try {
        if (authService.isAuthenticated()) {
          const user = await authService.getCurrentUser();
          if (user) {
            console.log('PaymentConfirmationForm - Current user loaded:', user);
            console.log('PaymentConfirmationForm - User ID:', user._id || user.id);
            console.log('PaymentConfirmationForm - User ID type:', typeof (user._id || user.id));
            setCurrentUser(user);
            setCurrentUserId(user._id || user.id);
          } else {
            console.log('PaymentConfirmationForm - No user returned from authService');
          }
        } else {
          console.log('PaymentConfirmationForm - User not authenticated');
        }
      } catch (error) {
        console.error('Error loading current user:', error);
        setCurrentUser(null);
        setCurrentUserId(null);
      }
    };

    // Load user on mount
    loadCurrentUser();

    // Listen for login events (e.g., from auto-login after consultation)
    const handleUserLogin = (event) => {
      console.log('PaymentConfirmationForm - User login event received:', event.detail);
      if (event.detail?.user) {
        setCurrentUser(event.detail.user);
        setCurrentUserId(event.detail.user._id || event.detail.user.id);
      }
    };

    window.addEventListener('user-login', handleUserLogin);

    // Cleanup
    return () => {
      window.removeEventListener('user-login', handleUserLogin);
    };
  }, []);

  const [formData, setFormData] = useState({
    amount: initialData?.amount || orderData?.total || servicePrice || '',
    paymentMethod: initialData?.paymentMethod || '',
    notes: initialData?.notes || '',
    proofImage: initialData?.proofImage || null
  });
  
  const [imagePreview, setImagePreview] = useState(initialData?.proofImage || null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [errors, setErrors] = useState({});

  // Load pending payment data from localStorage on component mount
  useEffect(() => {
    if (appointmentId && currentUserId && !isEditing) {
      const pendingData = loadPendingPaymentConfirmation(appointmentId, currentUserId);
      if (pendingData) {
        setFormData(prev => ({
          ...prev,
          amount: pendingData.amount || prev.amount,
          paymentMethod: pendingData.paymentMethod || prev.paymentMethod,
          notes: pendingData.notes || prev.notes,
          proofImage: pendingData.proofImage || prev.proofImage
        }));
        if (pendingData.proofImage) {
          setImagePreview(pendingData.proofImage);
        }
      }
    }
  }, [appointmentId, currentUserId, isEditing]);

  // Save form data to localStorage whenever it changes (except image)
  useEffect(() => {
    if (appointmentId && currentUserId && !isEditing) {
      const dataToSave = {
        amount: formData.amount,
        paymentMethod: formData.paymentMethod,
        notes: formData.notes,
        // Don't save image to localStorage until form submission
      };
      savePendingPaymentConfirmation(appointmentId, dataToSave, currentUserId);
    }
  }, [appointmentId, currentUserId, formData.amount, formData.paymentMethod, formData.notes, isEditing]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Please enter a valid amount';
    }

    if (!formData.paymentMethod.trim()) {
      newErrors.paymentMethod = 'Payment method is required';
    }

    if (!formData.proofImage) {
      newErrors.proofImage = 'Payment proof image is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleFileUpload = async (file) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      showError('Please upload a JPG, PNG, or PDF file');
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      showError('File size must be less than 5MB');
      return;
    }

    // Use request lock to prevent multiple simultaneous uploads
    const lockIdentifier = `${currentUserId || 'anonymous'}_${appointmentId || 'default'}`;

    try {
      await requestLock.withLock(OPERATIONS.IMAGE_UPLOAD, lockIdentifier, async () => {
        setIsUploading(true);

        try {
          // Convert file to base64 for upload
          const reader = new FileReader();
          reader.onload = (e) => {
            const base64 = e.target.result;
            setFormData(prev => ({ ...prev, proofImage: base64 }));
            setImagePreview(base64);
            if (errors.proofImage) {
              setErrors(prev => ({ ...prev, proofImage: null }));
            }
          };
          reader.readAsDataURL(file);
        } catch (error) {
          showError('Failed to process file');
          throw error; // Re-throw to ensure lock is released
        } finally {
          setIsUploading(false);
        }
      });
    } catch (error) {
      if (error.message.includes('already in progress')) {
        showError('File upload is already in progress. Please wait for the current upload to complete.');
      }
      // If it's a different error, it was already handled above
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const removeImage = () => {
    setFormData(prev => ({ ...prev, proofImage: null }));
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Check if user is authenticated
    if (!currentUserId) {
      showError('You must be logged in to submit payment confirmation. Please log in and try again.');
      return;
    }

    // Use request lock to prevent duplicate submissions
    const lockIdentifier = `${currentUserId}_${appointmentId}`;

    try {
      await requestLock.withLock(OPERATIONS.PAYMENT_CONFIRMATION, lockIdentifier, async () => {
        setIsSubmitting(true);

        try {
          const submitData = {
            ...formData,
            order: orderData?._id,
            appointment: appointmentId
          };

          console.log('PaymentConfirmationForm - Submitting payment confirmation:');
          console.log('- Current User ID:', currentUserId);
          console.log('- Current User ID type:', typeof currentUserId);
          console.log('- Appointment ID:', appointmentId);
          console.log('- Submit Data:', submitData);

          // Save complete payment data including image to localStorage before submission
          if (appointmentId && currentUserId) {
            savePendingPaymentConfirmation(appointmentId, {
              ...formData,
              status: 'submitted',
              submittedAt: new Date().toISOString()
            }, currentUserId);
          }

          await onSubmit(submitData);

          showSuccess(branding?.paymentConfirmation?.successMessage || 'Payment confirmation submitted successfully!');

          // Remove from pending payments after successful submission
          if (appointmentId && currentUserId) {
            removePendingPaymentConfirmation(appointmentId, currentUserId);
          }

          // Reset form if not editing
          if (!isEditing) {
            setFormData({ amount: '', paymentMethod: '', notes: '', proofImage: null });
            setImagePreview(null);
            if (fileInputRef.current) {
              fileInputRef.current.value = '';
            }
          }
        } catch (error) {
          console.error('Payment confirmation submission error:', error);
          showError(`Failed to ${isEditing ? 'update' : 'submit'} payment confirmation: ${error.message}`);
          throw error; // Re-throw to ensure lock is released
        } finally {
          setIsSubmitting(false);
        }
      });
    } catch (error) {
      if (error.message.includes('already in progress')) {
        showError('Payment confirmation is already being submitted. Please wait for the current request to complete.');
      }
      // If it's a different error, it was already handled above
    }
  };

  const isImage = (file) => {
    return file && (file.startsWith('data:image/') || file.includes('image'));
  };

  // Helper function to format time from 24-hour to 12-hour format
  const formatTime = (time24) => {
    if (!time24) return 'Time not set';

    try {
      // Handle both HH:MM and HH:MM:SS formats
      const timeParts = time24.split(':');
      if (timeParts.length < 2) return time24;

      let hours = parseInt(timeParts[0]);
      const minutes = timeParts[1];
      const ampm = hours >= 12 ? 'PM' : 'AM';

      // Convert to 12-hour format
      hours = hours % 12;
      hours = hours ? hours : 12; // 0 should be 12

      return `${hours}:${minutes} ${ampm}`;
    } catch (error) {
      return time24;
    }
  };

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {branding?.paymentConfirmation?.pageTitle || 'Payment Confirmation'}
        </h2>
        <p className="text-gray-600">
          {branding?.paymentConfirmation?.pageDescription || 'Please upload your payment proof to complete your order.'}
        </p>
        {appointment && (
          <div className="mt-4 space-y-3">
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                <p className="text-sm text-yellow-800">
                  <strong>Status:</strong> Your appointment is currently <strong>pending payment confirmation</strong>.
                  Please submit your payment proof to complete the booking process.
                </p>
              </div>
            </div>
            {!currentUserId && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-red-400 rounded-full mr-2"></div>
                  <p className="text-sm text-red-800">
                    <strong>Authentication Required:</strong> You must be logged in to submit payment confirmation.
                    Please log in to continue.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Reference Information */}
        <div className="bg-gray-50 rounded-xl p-4">
          <h3 className="font-medium text-gray-900 mb-3">Order Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {orderData && (
              <>
                <div>
                  <span className="text-gray-600">
                    {branding?.paymentConfirmation?.orderReferenceLabel || 'Order Reference'}:
                  </span>
                  <span className="ml-2 font-medium">{orderData.orderNumber}</span>
                </div>
                <div>
                  <span className="text-gray-600">Total Amount:</span>
                  <span className="ml-2 font-medium">${orderData.total}</span>
                </div>
              </>
            )}
            {appointment && (
              <>
                <div>
                  <span className="text-gray-600">
                    {branding?.paymentConfirmation?.appointmentReferenceLabel || 'Appointment Reference'}:
                  </span>
                  <span className="ml-2 font-medium">{appointmentId}</span>
                </div>
                <div>
                  <span className="text-gray-600">Service:</span>
                  <span className="ml-2 font-medium">{service?.name || 'N/A'}</span>
                </div>
                <div>
                  <span className="text-gray-600">Date:</span>
                  <span className="ml-2 font-medium">
                    {appointment.date ? new Date(appointment.date).toLocaleDateString() : 'N/A'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Time:</span>
                  <span className="ml-2 font-medium">{formatTime(appointment.time)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Status:</span>
                  <span className="ml-2 font-medium capitalize text-yellow-600">
                    {appointment.status || 'Pending'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Service Price:</span>
                  <span className="ml-2 font-medium">${service?.price || 0}</span>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Amount */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.paymentConfirmation?.amountLabel || 'Payment Amount'} *
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.amount}
              onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || '' }))}
              className={`w-full pl-8 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                errors.amount ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="0.00"
            />
          </div>
          {errors.amount && (
            <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
          )}
        </div>

        {/* Payment Method */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.paymentConfirmation?.paymentMethodLabel || 'Payment Method'} *
          </label>
          <input
            type="text"
            value={formData.paymentMethod}
            onChange={(e) => setFormData(prev => ({ ...prev, paymentMethod: e.target.value }))}
            className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
              errors.paymentMethod ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="e.g., Bank Transfer, PayPal, Cash App"
          />
          {errors.paymentMethod && (
            <p className="mt-1 text-sm text-red-600">{errors.paymentMethod}</p>
          )}
        </div>

        {/* File Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.paymentConfirmation?.uploadTitle || 'Upload Payment Proof'} *
          </label>
          <p className="text-sm text-gray-600 mb-4">
            {branding?.paymentConfirmation?.uploadInstructions || 'Please upload a clear image of your payment confirmation, receipt, or transaction screenshot.'}
          </p>

          {!imagePreview ? (
            <div
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors duration-200 ${
                errors.proofImage ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50'
              }`}
            >
              <FiUpload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-700 mb-2">
                {branding?.paymentConfirmation?.dragDropText || 'Drag and drop your image here'}
              </p>
              <p className="text-gray-600 mb-4">
                {branding?.paymentConfirmation?.browseFilesText || 'or browse files'}
              </p>
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="px-6 py-3 bg-blue-600 text-white font-medium rounded-xl hover:bg-blue-700 transition-colors duration-200"
                disabled={isUploading}
              >
                {isUploading 
                  ? (branding?.paymentConfirmation?.uploadingText || 'Uploading...')
                  : 'Choose File'
                }
              </button>
              <p className="text-xs text-gray-500 mt-2">
                {branding?.paymentConfirmation?.supportedFormats || 'Supported formats: JPG, PNG, PDF (max 5MB)'}
              </p>
            </div>
          ) : (
            <div className="border border-gray-300 rounded-xl p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">
                  {branding?.paymentConfirmation?.previewTitle || 'Image Preview'}
                </h4>
                <button
                  type="button"
                  onClick={removeImage}
                  className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                >
                  <FiX className="w-4 h-4" />
                </button>
              </div>
              
              <div className="flex items-center justify-center bg-gray-50 rounded-lg p-4">
                {isImage(imagePreview) ? (
                  <img
                    src={imagePreview}
                    alt="Payment proof preview"
                    className="max-w-full max-h-64 object-contain rounded-lg"
                  />
                ) : (
                  <div className="flex items-center space-x-2 text-gray-600">
                    <FiFile className="w-8 h-8" />
                    <span>PDF File Uploaded</span>
                  </div>
                )}
              </div>
            </div>
          )}

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*,.pdf"
            onChange={handleFileSelect}
            className="hidden"
          />
          
          {errors.proofImage && (
            <p className="mt-1 text-sm text-red-600">{errors.proofImage}</p>
          )}
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.paymentConfirmation?.notesLabel || 'Additional Notes'}
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            placeholder={branding?.paymentConfirmation?.notesPlaceholder || 'Any additional information about your payment...'}
            rows={3}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
            maxLength={500}
          />
          <p className="text-xs text-gray-500 mt-1">
            {formData.notes.length}/500
          </p>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <div className="flex items-center justify-end space-x-4">
            {onCancel && (
              <button
                type="button"
                onClick={onCancel}
                className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors duration-200"
                disabled={isSubmitting || isUploading}
              >
                Cancel
              </button>
            )}
            <button
              type="submit"
              disabled={isSubmitting || isUploading || !currentUserId}
              className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-xl hover:from-blue-700 hover:to-purple-700 focus:ring-4 focus:ring-blue-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {!currentUserId
                ? 'Please Log In to Submit'
                : isSubmitting
                ? (branding?.paymentConfirmation?.submittingText || 'Submitting...')
                : (branding?.paymentConfirmation?.submitButton || 'Submit Payment Proof')
              }
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default PaymentConfirmationForm;
