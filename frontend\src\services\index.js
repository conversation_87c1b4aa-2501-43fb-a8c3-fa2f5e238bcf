// Base API service
export { default as apiService } from './api.js'

// Authentication
export { default as authService } from './authService.js'

// Products and Shopping
export { default as productService } from './productService.js'
export { default as cartService } from './cartService.js'
export { default as orderService } from './orderService.js'

// Services and Appointments
export { default as serviceService } from './serviceService.js'
export { default as appointmentService } from './appointmentService.js'
export { consultationService } from './consultationService.js'

// User Management
export { default as userService } from './userService.js'
export { default as notificationService } from './notificationService.js'

// Admin
export { default as adminService } from './adminService.js'

// Content Management
export { default as contentService } from './contentService.js'

// Promotions and Loyalty
export { 
  default as discountService,
  giftCardService,
  loyaltyService 
} from './discountService.js'

// File Uploads
export { default as uploadService } from './uploadService.js'

// Additional services for specific features
export { default as paymentService } from './paymentService.js'
export { default as paymentConfirmationService } from './paymentConfirmationService.js'
export { default as referralService } from './referralService.js'

// Branding and Customization
export { brandingService } from './brandingService.js'

/**
 * Service manager for handling global service operations
 */
class ServiceManager {
  constructor() {
    this.services = new Map()
    this.initialized = false
  }

  /**
   * Initialize all services
   */
  async initialize() {
    if (this.initialized) {
      return
    }

    try {
      // Check API health
      const { default: apiService } = await import('./api.js')
      await apiService.healthCheck()
      
      // Initialize authentication state
      const { default: authService } = await import('./authService.js')
      if (authService.isAuthenticated()) {
        try {
          await authService.verifyToken()
        } catch (error) {
          console.warn('Token verification failed, clearing auth state')
          authService.logout()
        }
      }

      // Set up global error handlers
      this.setupGlobalErrorHandlers()

      this.initialized = true
      console.log('Services initialized successfully')
    } catch (error) {
      console.error('Failed to initialize services:', error)
      throw error
    }
  }

  /**
   * Set up global error handlers for all services
   */
  setupGlobalErrorHandlers() {
    // Listen for authentication errors
    window.addEventListener('auth-error', (event) => {
      console.warn('Authentication error:', event.detail)
      // Redirect to login or show auth modal
      window.dispatchEvent(new CustomEvent('show-login-modal'))
    })

    // Listen for network errors
    window.addEventListener('network-error', (event) => {
      console.error('Network error:', event.detail)
      // Show network error notification
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'Network error. Please check your connection.'
        }
      }))
    })

    // Listen for server errors
    window.addEventListener('server-error', (event) => {
      console.error('Server error:', event.detail)
      // Show server error notification
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'Server error. Please try again later.'
        }
      }))
    })
  }

  /**
   * Get service instance
   */
  getService(serviceName) {
    return this.services.get(serviceName)
  }

  /**
   * Register service instance
   */
  registerService(serviceName, serviceInstance) {
    this.services.set(serviceName, serviceInstance)
  }

  /**
   * Check if services are initialized
   */
  isInitialized() {
    return this.initialized
  }

  /**
   * Reset all services (useful for logout)
   */
  reset() {
    this.services.clear()
    this.initialized = false
  }
}

// Create and export singleton instance
export const serviceManager = new ServiceManager()

// Auto-initialize services when module is imported
if (typeof window !== 'undefined') {
  // Only initialize in browser environment
  serviceManager.initialize().catch(error => {
    console.error('Auto-initialization failed:', error)
  })
}

// Export default service manager
export default serviceManager
