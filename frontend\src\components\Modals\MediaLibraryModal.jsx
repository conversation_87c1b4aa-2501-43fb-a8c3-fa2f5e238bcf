import { useState, useEffect, useCallback } from 'react'
import { 
  FiX, FiSearch, FiUpload, FiImage, FiTrash2, FiEdit3, 
  FiGrid, FiList, FiFilter, FiCheck, FiPlus, FiRefreshCw 
} from 'react-icons/fi'
import { apiService } from '../../services/index.js'
import uploadService from '../../services/uploadService'

const MediaLibraryModal = ({
  isOpen,
  onClose,
  onSelect,
  multiple = false,
  selectedImages = [],
  branding,
  allowUpload = true
}) => {
  const [media, setMedia] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedItems, setSelectedItems] = useState(selectedImages)
  const [viewMode, setViewMode] = useState('grid') // 'grid' or 'list'
  const [filterType, setFilterType] = useState('all') // 'all', 'image', 'video', 'document'
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState({})

  // Fetch media library
  const fetchMedia = useCallback(async (page = 1, search = '', mimeType = '') => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        ...(search && { search }),
        ...(mimeType && { mimeType })
      })

      const response = await apiService.get(`/media/library?${params}`)
      
      if (response.success) {
        setMedia(response.data.media)
        setTotalPages(response.data.pagination.totalPages)
        setCurrentPage(response.data.pagination.currentPage)
      }
    } catch (error) {
      console.error('Error fetching media:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  // Load media on mount and when filters change
  useEffect(() => {
    if (isOpen) {
      const mimeTypeFilter = filterType === 'all' ? '' : 
        filterType === 'image' ? 'image/' :
        filterType === 'video' ? 'video/' :
        filterType === 'document' ? 'application/' : ''
      
      fetchMedia(1, searchTerm, mimeTypeFilter)
    }
  }, [isOpen, searchTerm, filterType, fetchMedia])

  // Handle file upload
  const handleFileUpload = async (files) => {
    if (!files || files.length === 0) return

    setUploading(true)
    const uploadPromises = Array.from(files).map(async (file, index) => {
      try {
        setUploadProgress(prev => ({ ...prev, [index]: 0 }))
        
        const formData = new FormData()
        formData.append('file', file)
        formData.append('alt', '')
        formData.append('caption', '')
        formData.append('description', '')
        formData.append('tags', JSON.stringify([]))

        const response = await apiService.post('/media/upload', formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
          onUploadProgress: (progressEvent) => {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            setUploadProgress(prev => ({ ...prev, [index]: progress }))
          }
        })

        if (response.success) {
          return response.data.media
        }
        throw new Error('Upload failed')
      } catch (error) {
        console.error('Error uploading file:', error)
        return null
      }
    })

    const results = await Promise.all(uploadPromises)
    const successfulUploads = results.filter(Boolean)
    
    if (successfulUploads.length > 0) {
      // Refresh media library
      fetchMedia(currentPage, searchTerm, filterType === 'all' ? '' : filterType)
    }

    setUploading(false)
    setUploadProgress({})
  }

  // Handle item selection
  const handleItemSelect = (item) => {
    if (multiple) {
      setSelectedItems(prev => {
        const isSelected = prev.some(selected => selected._id === item._id)
        if (isSelected) {
          return prev.filter(selected => selected._id !== item._id)
        } else {
          return [...prev, item]
        }
      })
    } else {
      setSelectedItems([item])
    }
  }

  // Handle selection confirmation
  const handleConfirmSelection = () => {
    onSelect(multiple ? selectedItems : selectedItems[0])
    onClose()
  }

  // Handle delete media
  const handleDeleteMedia = async (mediaId) => {
    if (!confirm('Are you sure you want to delete this media item?')) return

    try {
      const response = await apiService.delete(`/media/${mediaId}`)
      if (response.success) {
        // Refresh media library
        fetchMedia(currentPage, searchTerm, filterType === 'all' ? '' : filterType)
      }
    } catch (error) {
      console.error('Error deleting media:', error)
    }
  }

  // Get file type icon
  const getFileTypeIcon = (mimeType) => {
    if (mimeType.startsWith('image/')) return FiImage
    if (mimeType.startsWith('video/')) return FiImage // You can add video icon
    return FiImage // Default to image icon
  }

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <FiImage className="w-6 h-6 mr-3" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
            <h2 className="text-2xl font-bold text-gray-900">Media Library</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <FiX className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Toolbar */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search media..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
              />
            </div>

            {/* Filter */}
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Media</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="document">Documents</option>
            </select>

            {/* View Mode */}
            <div className="flex border border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
              >
                <FiGrid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
              >
                <FiList className="w-4 h-4" />
              </button>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Upload Button */}
            {allowUpload && (
              <div>
                <input
                  type="file"
                  multiple
                  accept="image/*,video/*,.pdf,.doc,.docx"
                  onChange={(e) => handleFileUpload(e.target.files)}
                  className="hidden"
                  id="media-upload"
                />
                <label
                  htmlFor="media-upload"
                  className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 cursor-pointer"
                >
                  <FiUpload className="w-4 h-4 mr-2" />
                  Upload Files
                </label>
              </div>
            )}

            {/* Refresh */}
            <button
              onClick={() => fetchMedia(currentPage, searchTerm, filterType === 'all' ? '' : filterType)}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              <FiRefreshCw className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-hidden flex">
          {/* Media Grid/List */}
          <div className="flex-1 overflow-y-auto p-4">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
            ) : media.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <FiImage className="w-16 h-16 mb-4" />
                <p className="text-lg font-medium">No media found</p>
                <p className="text-sm">Upload some files to get started</p>
              </div>
            ) : viewMode === 'grid' ? (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {media.map((item) => {
                  const isSelected = selectedItems.some(selected => selected._id === item._id)
                  const FileIcon = getFileTypeIcon(item.mimeType)
                  
                  return (
                    <div
                      key={item._id}
                      className={`relative group cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                        isSelected 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleItemSelect(item)}
                    >
                      <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                        {item.mimeType.startsWith('image/') ? (
                          <img
                            src={item.url}
                            alt={item.alt || item.originalName}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <FileIcon className="w-8 h-8 text-gray-400" />
                          </div>
                        )}
                      </div>
                      
                      {/* Selection indicator */}
                      {isSelected && (
                        <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                          <FiCheck className="w-3 h-3" />
                        </div>
                      )}
                      
                      {/* File info */}
                      <div className="p-2">
                        <p className="text-xs text-gray-600 truncate" title={item.originalName}>
                          {item.originalName}
                        </p>
                        <p className="text-xs text-gray-400">
                          {formatFileSize(item.size)}
                        </p>
                      </div>
                      
                      {/* Actions */}
                      <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteMedia(item._id)
                          }}
                          className="bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors duration-200"
                        >
                          <FiTrash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              // List view implementation would go here
              <div className="space-y-2">
                {media.map((item) => {
                  const isSelected = selectedItems.some(selected => selected._id === item._id)
                  const FileIcon = getFileTypeIcon(item.mimeType)
                  
                  return (
                    <div
                      key={item._id}
                      className={`flex items-center p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                        isSelected 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleItemSelect(item)}
                    >
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                        {item.mimeType.startsWith('image/') ? (
                          <img
                            src={item.url}
                            alt={item.alt || item.originalName}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <FileIcon className="w-6 h-6 text-gray-400" />
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{item.originalName}</p>
                        <p className="text-sm text-gray-500">
                          {formatFileSize(item.size)} • {new Date(item.uploadedAt).toLocaleDateString()}
                        </p>
                      </div>
                      
                      {isSelected && (
                        <FiCheck className="w-5 h-5 text-blue-500" />
                      )}
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {selectedItems.length > 0 && (
              <span>{selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''} selected</span>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirmSelection}
              disabled={selectedItems.length === 0}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              Select {selectedItems.length > 0 ? `(${selectedItems.length})` : ''}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MediaLibraryModal
