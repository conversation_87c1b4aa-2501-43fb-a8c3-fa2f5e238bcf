import { useState } from 'react'
import { FiShoppingBag, FiPackage, FiTruck, FiCheckCircle, FiEye, FiDownload, FiRefreshCw } from 'react-icons/fi'
import { useBranding } from '../../../contexts/BrandingContext'

const UserOrders = ({ 
  orders, 
  sectionLoading,
  setViewingItem,
  setModalType,
  setEditingItem
}) => {
  const { branding } = useBranding()
  const [filterStatus, setFilterStatus] = useState('all')

  const filteredOrders = orders.filter(order => {
    if (filterStatus === 'all') return true
    return order.status === filterStatus
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'shipped':
        return 'bg-blue-100 text-blue-800'
      case 'processing':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'delivered':
        return <FiCheckCircle className="w-4 h-4" />
      case 'shipped':
        return <FiTruck className="w-4 h-4" />
      case 'processing':
        return <FiPackage className="w-4 h-4" />
      default:
        return <FiShoppingBag className="w-4 h-4" />
    }
  }

  return (
    <div className="space-y-4 sm:space-y-6 w-full max-w-full overflow-x-hidden">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 truncate">My Orders</h2>
          <p className="text-gray-600 mt-1 text-sm sm:text-base">Track and manage your order history</p>
        </div>
        <div className="flex items-center space-x-3 flex-shrink-0">
          <button className="flex items-center justify-center px-3 sm:px-4 lg:px-6 py-2 sm:py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 cursor-pointer w-full sm:w-auto text-sm sm:text-base">
            <FiRefreshCw className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Refresh </span>Orders
          </button>
        </div>
      </div>

      {/* Order Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">
                {sectionLoading?.orders ? (
                  <div className="h-6 bg-gray-300 rounded w-8 animate-pulse"></div>
                ) : (
                  orders.length
                )}
              </p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <FiShoppingBag className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Spent</p>
              <p className="text-2xl font-bold text-gray-900">
                ${orders.reduce((sum, order) => sum + (order.total || 0), 0).toFixed(2)}
              </p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <FiCheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Delivered</p>
              <p className="text-2xl font-bold text-gray-900">
                {orders.filter(order => order.status === 'delivered').length}
              </p>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg">
              <FiTruck className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">In Transit</p>
              <p className="text-2xl font-bold text-gray-900">
                {orders.filter(order => order.status === 'shipped').length}
              </p>
            </div>
            <div className="p-3 bg-orange-50 rounded-lg">
              <FiPackage className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg border border-white/20 w-full max-w-full">
        <div className="flex flex-wrap gap-2 sm:gap-3">
          {[
            { id: 'all', label: 'All Orders', count: orders.length },
            { id: 'processing', label: 'Processing', count: orders.filter(o => o.status === 'processing').length },
            { id: 'shipped', label: 'Shipped', count: orders.filter(o => o.status === 'shipped').length },
            { id: 'delivered', label: 'Delivered', count: orders.filter(o => o.status === 'delivered').length },
            { id: 'cancelled', label: 'Cancelled', count: orders.filter(o => o.status === 'cancelled').length }
          ].map((filter) => (
            <button
              key={filter.id}
              onClick={() => setFilterStatus(filter.id)}
              className={`px-3 sm:px-4 py-2 sm:py-2.5 rounded-lg transition-colors duration-200 text-xs sm:text-sm font-medium ${
                filterStatus === filter.id
                  ? 'bg-green-50 text-green-700 border border-green-200'
                  : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-transparent'
              }`}
            >
              <span className="hidden sm:inline">{filter.label} </span>
              <span className="sm:hidden">{filter.label.split(' ')[0]} </span>
              ({filter.count})
            </button>
          ))}
        </div>
      </div>

      {/* Orders List */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        {sectionLoading.orders ? (
          <div className="p-6">
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl animate-pulse">
                  <div className="w-16 h-16 bg-gray-300 rounded-xl"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-32"></div>
                    <div className="h-3 bg-gray-300 rounded w-24"></div>
                    <div className="h-3 bg-gray-300 rounded w-40"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-16"></div>
                    <div className="h-6 bg-gray-300 rounded w-20"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : filteredOrders.length > 0 ? (
          <>
            {/* Desktop Layout */}
            <div className="hidden md:block">
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="divide-y divide-gray-200">
                  {filteredOrders.map((order, index) => (
                    <div key={index} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <div className="w-16 h-16 rounded-xl flex items-center justify-center bg-gradient-to-br from-orange-500 to-orange-600">
                            <FiShoppingBag className="w-8 h-8 text-white" />
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">
                                Order #{order.id || `ORD-${index + 1}`}
                              </h3>
                              <span className={`px-3 py-1 rounded-full text-xs font-medium flex items-center ${getStatusColor(order.status)}`}>
                                {getStatusIcon(order.status)}
                                <span className="ml-1">{order.status || 'Processing'}</span>
                              </span>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
                              <div>
                                <span className="font-medium">Order Date:</span> {order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'Recently'}
                              </div>
                              <div>
                                <span className="font-medium">Total:</span> ${order.total?.toFixed(2) || '0.00'}
                              </div>
                              <div>
                                <span className="font-medium">Items:</span> {order.items?.length || 0} items
                              </div>
                              {order.trackingNumber && (
                                <div>
                                  <span className="font-medium">Tracking:</span> {order.trackingNumber}
                                </div>
                              )}
                            </div>

                            {order.items && order.items.length > 0 && (
                              <div className="bg-gray-50 rounded-lg p-3">
                                <p className="text-sm font-medium text-gray-700 mb-2">Items:</p>
                                <div className="space-y-1">
                                  {order.items.slice(0, 3).map((item, itemIndex) => (
                                    <div key={itemIndex} className="flex justify-between text-sm text-gray-600">
                                      <span>{item.quantity}x {item.name}</span>
                                      <span>${(item.price * item.quantity).toFixed(2)}</span>
                                    </div>
                                  ))}
                                  {order.items.length > 3 && (
                                    <p className="text-xs text-gray-500">
                                      +{order.items.length - 3} more items
                                    </p>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-col space-y-2 ml-4">
                          <button
                            onClick={() => {
                              setViewingItem(order)
                              setEditingItem(order)
                              setModalType('view')
                            }}
                            className="flex items-center px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer text-sm"
                          >
                            <FiEye className="w-4 h-4 mr-1" />
                            View Details
                          </button>

                          {order.status === 'delivered' && (
                            <button className="flex items-center px-3 py-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer text-sm">
                              <FiDownload className="w-4 h-4 mr-1" />
                              Download Invoice
                            </button>
                          )}

                          {order.trackingNumber && (
                            <button className="flex items-center px-3 py-2 text-purple-600 hover:bg-purple-50 rounded-lg transition-colors duration-200 cursor-pointer text-sm">
                              <FiTruck className="w-4 h-4 mr-1" />
                              Track Package
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Mobile Card Layout */}
            <div className="block md:hidden w-full">
              <div className="space-y-3 p-3 sm:p-4 w-full">
                {filteredOrders.map((order, index) => (
                  <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg border border-white/20 w-full max-w-full overflow-hidden">
                    <div className="flex items-start justify-between mb-4 gap-3">
                      <div className="flex items-center min-w-0 flex-1">
                        <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center bg-gradient-to-br from-orange-500 to-orange-600">
                          <FiShoppingBag className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div className="ml-3 sm:ml-4 min-w-0 flex-1">
                          <div className="font-semibold text-gray-900 text-sm sm:text-base truncate">
                            Order #{order.id || `ORD-${index + 1}`}
                          </div>
                          <div className="text-xs sm:text-sm text-gray-500 truncate">
                            {order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'Recently'}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                        <button
                          onClick={() => {
                            setViewingItem(order)
                            setEditingItem(order)
                            setModalType('view')
                          }}
                          className="p-1.5 sm:p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="View Details"
                        >
                          <FiEye className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                        </button>
                        {order.status === 'delivered' && (
                          <button
                            className="p-1.5 sm:p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                            title="Download Invoice"
                          >
                            <FiDownload className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                          </button>
                        )}
                        {order.trackingNumber && (
                          <button
                            className="p-1.5 sm:p-2 text-purple-600 hover:bg-purple-50 rounded-lg transition-colors duration-200 cursor-pointer"
                            title="Track Package"
                          >
                            <FiTruck className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                          </button>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2 sm:space-y-3">
                      <div className="flex justify-between items-center gap-2">
                        <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Status:</span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                          {getStatusIcon(order.status)}
                          <span className="ml-1">{order.status || 'Processing'}</span>
                        </span>
                      </div>
                      <div className="flex justify-between items-center gap-2">
                        <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Items:</span>
                        <span className="text-xs sm:text-sm text-gray-900 text-right">{order.items?.length || 0} item{(order.items?.length || 0) > 1 ? 's' : ''}</span>
                      </div>
                      <div className="flex justify-between items-center gap-2">
                        <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Total:</span>
                        <span className="text-sm sm:text-base font-bold text-gray-900">${order.total?.toFixed(2) || '0.00'}</span>
                      </div>
                      {order.trackingNumber && (
                        <div className="flex justify-between items-center gap-2">
                          <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Tracking:</span>
                          <span className="text-xs sm:text-sm text-gray-900 text-right font-mono">{order.trackingNumber}</span>
                        </div>
                      )}

                      {/* Items Preview */}
                      {order.items && order.items.length > 0 && (
                        <div className="pt-2 border-t border-gray-200">
                          <span className="text-xs sm:text-sm font-medium text-gray-500">Items:</span>
                          <div className="mt-1 space-y-1">
                            {order.items.slice(0, 2).map((item, itemIndex) => (
                              <div key={itemIndex} className="flex justify-between items-center">
                                <span className="text-xs sm:text-sm text-gray-700 truncate">{item.quantity}x {item.name}</span>
                                <span className="text-xs sm:text-sm text-gray-500 flex-shrink-0">${(item.price * item.quantity).toFixed(2)}</span>
                              </div>
                            ))}
                            {order.items.length > 2 && (
                              <p className="text-xs sm:text-sm text-gray-500">
                                +{order.items.length - 2} more items
                              </p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        ) : (
        ) : (
          <div className="text-center py-12">
            <FiShoppingBag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {filterStatus === 'all' ? 'No orders found' : `No ${filterStatus} orders`}
            </h3>
            <p className="text-gray-600 mb-6">
              {filterStatus === 'all' 
                ? 'Start shopping to see your orders here.'
                : `You don't have any ${filterStatus} orders.`
              }
            </p>
            {filterStatus === 'all' && (
              <button className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 cursor-pointer">
                <FiShoppingBag className="w-4 h-4 mr-2" />
                Start Shopping
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default UserOrders
