import { Request } from 'express';
import { Document, Types } from 'mongoose';

// User Types
export interface IUser extends Document {
  _id: string;
  name: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  password: string;
  role: 'user' | 'admin';
  isVerified: boolean;
  resetPasswordToken?: string;
  resetPasswordExpires?: Date;
  favorites: string[];
  notificationPreferences: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

// Service Types
export interface IService extends Document {
  _id: string;
  name: string;
  description: string;
  category: string;
  duration: number; // in minutes
  price: number;
  isActive: boolean;
  image?: string;
  images?: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Product Types
export interface IProduct extends Document {
  _id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  stock: number;
  images: string[];
  isActive: boolean;
  rating: number;
  reviewCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// Appointment Types
export interface IAppointment extends Document {
  _id: string;
  user: Types.ObjectId;
  service: Types.ObjectId;
  date: Date;
  time: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  type: 'consultation' | 'service';
  paymentStatus: 'pending' | 'paid' | 'refunded';
  customerInfo: {
    name: string;
    email: string;
    phone: string;
  };
  message?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Cart Types
export interface ICartItem {
  product: Types.ObjectId;
  quantity: number;
  price: number;
}

export interface ICart extends Document {
  _id: string;
  user: Types.ObjectId;
  items: ICartItem[];
  totalAmount: number;
  createdAt: Date;
  updatedAt: Date;
}

// Order Types
export interface IOrderItem {
  product: Types.ObjectId;
  quantity: number;
  price: number;
  name: string;
}

export interface IShippingAddress {
  street: string;
  city: string;
  state: string;
  zip: string;
}

export interface IOrder extends Document {
  _id: string;
  user: Types.ObjectId;
  orderNumber: string;
  items: IOrderItem[];
  totalAmount: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  shippingAddress: IShippingAddress;
  paymentMethod: string;
  paymentStatus: 'pending' | 'paid' | 'failed';
  createdAt: Date;
  updatedAt: Date;
}

// Review Types
export interface IReview extends Document {
  _id: string;
  user: Types.ObjectId;
  product?: Types.ObjectId;
  service?: Types.ObjectId;
  rating: number;
  title: string;
  comment: string;
  status: 'pending' | 'approved' | 'rejected';
  isVerifiedPurchase: boolean;
  helpfulVotes: number;
  createdAt: Date;
  updatedAt: Date;
}

// Payment Confirmation Types
export interface IPaymentConfirmation extends Document {
  _id: string;
  user: Types.ObjectId;
  order?: Types.ObjectId;
  appointment?: Types.ObjectId;
  amount: number;
  paymentMethod: string;
  proofImage: string;
  notes?: string;
  status: 'pending' | 'verified' | 'rejected';
  verifiedBy?: Types.ObjectId;
  verifiedAt?: Date;
  rejectionReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Notification Types
export interface INotification extends Document {
  _id: string;
  user: Types.ObjectId;
  title: string;
  message: string;
  type: 'appointment' | 'order' | 'general';
  isRead: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Request Types
export interface AuthenticatedRequest extends Request {
  user?: IUser;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

// Query Types
export interface PaginationQuery {
  page?: number;
  limit?: number;
}

export interface ProductQuery extends PaginationQuery {
  category?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
}

export interface AppointmentQuery {
  date?: string;
  status?: string;
  service?: string;
}

export interface AdminQuery extends PaginationQuery {
  search?: string;
  status?: string;
  date?: string;
  category?: string;
  isActive?: string;
}

// JWT Payload
export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  exp?: number;
  iat?: number;
}

// Email Types
export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}
